//
//  ContentView.swift
//  midi
//
//  Created by lincoo on 7/14/25.
//

import SwiftUI

struct ContentView: View {
    @State private var showGame = false
    @State private var showSongSelect = false
    @State private var showSettings = false
    @State private var selectedSong: Song?
    @State private var gameSettings = GameSettings()

    var body: some View {
        NavigationView {
            ZStack {
                // Background
                LinearGradient(
                    colors: [Color.purple.opacity(0.8), Color.blue.opacity(0.6)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()

                VStack(spacing: 30) {
                    // Header with settings button
                    HStack {
                        Spacer()
                        Button(action: {
                            showSettings = true
                        }) {
                            Image(systemName: "gearshape.fill")
                                .font(.title2)
                                .foregroundColor(.white)
                        }
                    }
                    .padding(.top)

                    // Title
                    VStack(spacing: 10) {
                        Text("MIDI")
                            .font(.system(size: 60, weight: .bold, design: .rounded))
                            .foregroundColor(.white)

                        Text("RHYTHM GAME")
                            .font(.title2)
                            .fontWeight(.medium)
                            .foregroundColor(.white.opacity(0.8))
                    }

                    // Subtitle
                    Text("Connect your 25-key MIDI keyboard and play!")
                        .font(.headline)
                        .foregroundColor(.white.opacity(0.9))
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)

                    Spacer()

                    // Buttons
                    VStack(spacing: 20) {
                        // Quick Play Button
                        Button(action: {
                            showGame = true
                        }) {
                            HStack {
                                Image(systemName: "play.fill")
                                Text("QUICK PLAY")
                                    .fontWeight(.bold)
                            }
                            .font(.title2)
                            .foregroundColor(.white)
                            .padding(.horizontal, 40)
                            .padding(.vertical, 15)
                            .background(
                                RoundedRectangle(cornerRadius: 25)
                                    .fill(Color.green)
                                    .shadow(color: .black.opacity(0.3), radius: 10, x: 0, y: 5)
                            )
                        }

                        // Song Select Button
                        Button(action: {
                            showSongSelect = true
                        }) {
                            HStack {
                                Image(systemName: "music.note.list")
                                Text("SELECT SONG")
                                    .fontWeight(.bold)
                            }
                            .font(.title2)
                            .foregroundColor(.white)
                            .padding(.horizontal, 40)
                            .padding(.vertical, 15)
                            .background(
                                RoundedRectangle(cornerRadius: 25)
                                    .fill(Color.blue)
                                    .shadow(color: .black.opacity(0.3), radius: 10, x: 0, y: 5)
                            )
                        }
                    }

                    // Instructions
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Instructions:")
                            .font(.headline)
                            .foregroundColor(.white)

                        Text("• Connect a 25-key MIDI keyboard")
                        Text("• Hit the keys when notes reach the line")
                        Text("• Perfect timing = more points!")
                        Text("• Build combos for higher scores")
                    }
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 15)
                            .fill(Color.black.opacity(0.3))
                    )

                    Spacer()
                }
                .padding()
            }
        }
        .sheet(isPresented: $showGame) {
            if let song = selectedSong {
                GameView(song: song)
            } else {
                GameView()
            }
        }
        .sheet(isPresented: $showSongSelect) {
            SongSelectView(isPresented: $showSongSelect) { song in
                selectedSong = song
                showGame = true
            }
        }
        .sheet(isPresented: $showSettings) {
            SettingsView(settings: $gameSettings, isPresented: $showSettings)
        }
    }
}

#Preview {
    ContentView()
}
