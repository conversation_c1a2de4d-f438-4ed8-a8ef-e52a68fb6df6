//
//  GameView.swift
//  midi
//
//  Created by lincoo on 7/14/25.
//

import SwiftUI

struct GameView: View {
    @StateObject private var gameEngine = GameEngine()
    @StateObject private var midiManager = MIDIManager()
    @Environment(\.dismiss) private var dismiss

    let song: Song?
    let keyWidth: CGFloat = 40
    let keyHeight: CGFloat = 120
    let trackHeight: CGFloat = 600

    init(song: Song? = nil) {
        self.song = song
    }
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Background
                LinearGradient(
                    colors: [Color.black, Color.blue.opacity(0.3)],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Top UI
                    topUI
                        .padding()

                    // Game Area
                    gameArea

                    // Keyboard
                    keyboardView
                        .padding(.bottom)
                }

                // Overlays
                if gameEngine.gameState == .paused {
                    pauseOverlay
                }

                if gameEngine.gameState == .gameOver {
                    GameOverView(
                        stats: gameEngine.stats,
                        onRestart: {
                            if let song = song {
                                gameEngine.startGame(with: song)
                            } else {
                                gameEngine.startDemoSong()
                            }
                        },
                        onMainMenu: {
                            dismiss()
                        }
                    )
                }
            }
        }
        .onAppear {
            setupGame()
        }
    }
    
    private var topUI: some View {
        HStack {
            // Pause/Back button
            Button(action: {
                if gameEngine.gameState == .playing {
                    gameEngine.pauseGame()
                } else {
                    dismiss()
                }
            }) {
                Image(systemName: gameEngine.gameState == .playing ? "pause.fill" : "xmark")
                    .font(.title2)
                    .foregroundColor(.white)
            }

            Spacer()

            // Score
            VStack(alignment: .leading) {
                Text("SCORE")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                Text("\(gameEngine.stats.score)")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
            }

            Spacer()

            // Combo
            VStack {
                Text("COMBO")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                Text("\(gameEngine.stats.combo)")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.yellow)
            }

            Spacer()

            // Accuracy
            VStack(alignment: .trailing) {
                Text("ACCURACY")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                Text("\(String(format: "%.1f", gameEngine.stats.accuracy))%")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.green)
            }
        }
    }
    
    private var gameArea: some View {
        ZStack {
            // Note tracks
            HStack(spacing: 2) {
                ForEach(0..<MIDIKeyMapping.keyCount, id: \.self) { keyIndex in
                    noteTrack(for: keyIndex)
                }
            }
            .frame(height: trackHeight)
            
            // Hit line
            Rectangle()
                .fill(Color.white.opacity(0.8))
                .frame(height: 3)
                .offset(y: trackHeight/2 - 60)
            
            // Judgment display
            if let lastJudgment = gameEngine.lastJudgment {
                judgmentDisplay(lastJudgment)
                    .offset(y: -100)
            }
        }
    }
    
    private func noteTrack(for keyIndex: Int) -> some View {
        ZStack {
            // Track background
            Rectangle()
                .fill(isBlackKey(keyIndex) ? Color.gray.opacity(0.3) : Color.white.opacity(0.1))
                .frame(width: keyWidth)
            
            // Track border
            Rectangle()
                .stroke(Color.white.opacity(0.3), lineWidth: 1)
                .frame(width: keyWidth)
            
            // Notes in this track
            ForEach(gameEngine.activeNotes.filter { $0.keyIndex == keyIndex }) { note in
                noteView(note)
            }
            
            // Hit effect
            if midiManager.pressedKeys.contains(keyIndex) {
                Rectangle()
                    .fill(Color.yellow.opacity(0.5))
                    .frame(width: keyWidth, height: 60)
                    .offset(y: trackHeight/2 - 60)
            }
        }
    }
    
    private func noteView(_ note: Note) -> some View {
        Rectangle()
            .fill(isBlackKey(note.keyIndex) ? Color.purple : Color.cyan)
            .frame(width: keyWidth - 4, height: 20)
            .offset(y: note.currentY - trackHeight/2)
            .shadow(color: .white.opacity(0.5), radius: 2)
    }
    
    private func judgmentDisplay(_ judgment: HitJudgment) -> some View {
        Text(judgment.text)
            .font(.title)
            .fontWeight(.bold)
            .foregroundColor(judgment.color)
            .scaleEffect(1.5)
            .opacity(0.8)
    }
    
    private var keyboardView: some View {
        HStack(spacing: 2) {
            ForEach(0..<MIDIKeyMapping.keyCount, id: \.self) { keyIndex in
                keyView(for: keyIndex)
            }
        }
        .padding(.horizontal)
    }
    
    private func keyView(for keyIndex: Int) -> some View {
        let isPressed = midiManager.pressedKeys.contains(keyIndex)
        let isBlack = isBlackKey(keyIndex)
        
        return Rectangle()
            .fill(isPressed ? Color.yellow : (isBlack ? Color.black : Color.white))
            .frame(width: keyWidth, height: keyHeight)
            .overlay(
                Rectangle()
                    .stroke(Color.gray, lineWidth: 1)
            )
            .overlay(
                VStack {
                    Spacer()
                    Text(MIDIKeyMapping.keyName(for: keyIndex))
                        .font(.caption2)
                        .foregroundColor(isBlack ? .white : .black)
                        .padding(.bottom, 4)
                }
            )
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
    }
    
    private func isBlackKey(_ keyIndex: Int) -> Bool {
        let noteInOctave = keyIndex % 12
        return [1, 3, 6, 8, 10].contains(noteInOctave)
    }
    
    private var pauseOverlay: some View {
        ZStack {
            Color.black.opacity(0.7)
                .ignoresSafeArea()

            VStack(spacing: 30) {
                Text("PAUSED")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.white)

                VStack(spacing: 15) {
                    Button(action: {
                        gameEngine.resumeGame()
                    }) {
                        HStack {
                            Image(systemName: "play.fill")
                            Text("RESUME")
                                .fontWeight(.bold)
                        }
                        .font(.title2)
                        .foregroundColor(.white)
                        .padding(.horizontal, 40)
                        .padding(.vertical, 15)
                        .background(
                            RoundedRectangle(cornerRadius: 25)
                                .fill(Color.green)
                        )
                    }

                    Button(action: {
                        dismiss()
                    }) {
                        HStack {
                            Image(systemName: "house")
                            Text("MAIN MENU")
                                .fontWeight(.bold)
                        }
                        .font(.title2)
                        .foregroundColor(.white)
                        .padding(.horizontal, 40)
                        .padding(.vertical, 15)
                        .background(
                            RoundedRectangle(cornerRadius: 25)
                                .fill(Color.blue)
                        )
                    }
                }
            }
        }
    }

    private func setupGame() {
        midiManager.delegate = gameEngine
        gameEngine.midiManager = midiManager

        // Start with selected song or demo
        if let song = song {
            gameEngine.startGame(with: song)
        } else {
            gameEngine.startDemoSong()
        }
    }
}

#Preview {
    GameView()
}
