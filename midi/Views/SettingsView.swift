//
//  SettingsView.swift
//  midi
//
//  Created by lincoo on 7/14/25.
//

import SwiftUI

struct SettingsView: View {
    @Binding var settings: GameSettings
    @Binding var isPresented: Bool
    
    var body: some View {
        NavigationView {
            Form {
                Section("Gameplay") {
                    VStack(alignment: .leading) {
                        Text("Note Speed: \(String(format: "%.1f", settings.noteSpeed))x")
                        Slider(value: $settings.noteSpeed, in: 0.5...3.0, step: 0.1)
                    }
                    
                    VStack(alignment: .leading) {
                        Text("Hit Window: \(Int(settings.hitWindow * 1000))ms")
                        Slider(value: $settings.hitWindow, in: 0.05...0.2, step: 0.01)
                    }
                    
                    VStack(alignment: .leading) {
                        Text("Good Window: \(Int(settings.goodWindow * 1000))ms")
                        Slider(value: $settings.goodWindow, in: 0.1...0.4, step: 0.01)
                    }
                }
                
                Section("Audio") {
                    VStack(alignment: .leading) {
                        Text("Master Volume: \(Int(settings.masterVolume * 100))%")
                        Slider(value: $settings.masterVolume, in: 0...1, step: 0.1)
                    }
                    
                    VStack(alignment: .leading) {
                        Text("Music Volume: \(Int(settings.musicVolume * 100))%")
                        Slider(value: $settings.musicVolume, in: 0...1, step: 0.1)
                    }
                    
                    VStack(alignment: .leading) {
                        Text("Effect Volume: \(Int(settings.effectVolume * 100))%")
                        Slider(value: $settings.effectVolume, in: 0...1, step: 0.1)
                    }
                }
                
                Section("Display") {
                    Toggle("Show Keyboard", isOn: $settings.showKeyboard)
                    Toggle("Show FPS", isOn: $settings.showFPS)
                }
                
                Section("MIDI") {
                    Text("Connect your 25-key MIDI keyboard via USB")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("Supported range: C3 to C5 (25 keys)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .navigationTitle("Settings")
            .toolbar {
                ToolbarItem(placement: .primaryAction) {
                    Button("Done") {
                        isPresented = false
                    }
                }
            }
        }
    }
}

#Preview {
    SettingsView(
        settings: .constant(GameSettings()),
        isPresented: .constant(true)
    )
}
