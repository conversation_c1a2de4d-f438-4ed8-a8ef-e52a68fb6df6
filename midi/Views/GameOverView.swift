//
//  GameOverView.swift
//  midi
//
//  Created by lincoo on 7/14/25.
//

import SwiftUI

struct GameOverView: View {
    let stats: GameStats
    let onRestart: () -> Void
    let onMainMenu: () -> Void
    
    var body: some View {
        ZStack {
            // Background
            Color.black.opacity(0.8)
                .ignoresSafeArea()
            
            VStack(spacing: 30) {
                // Title
                Text("GAME OVER")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                // Stats
                VStack(spacing: 20) {
                    // Score
                    VStack {
                        Text("FINAL SCORE")
                            .font(.headline)
                            .foregroundColor(.white.opacity(0.8))
                        Text("\(stats.score)")
                            .font(.system(size: 48, weight: .bold))
                            .foregroundColor(.yellow)
                    }
                    
                    // Accuracy
                    VStack {
                        Text("ACCURACY")
                            .font(.headline)
                            .foregroundColor(.white.opacity(0.8))
                        Text("\(String(format: "%.1f", stats.accuracy))%")
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(.green)
                    }
                    
                    // Max Combo
                    VStack {
                        Text("MAX COMBO")
                            .font(.headline)
                            .foregroundColor(.white.opacity(0.8))
                        Text("\(stats.maxCombo)")
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(.orange)
                    }
                    
                    // Detailed Stats
                    HStack(spacing: 40) {
                        VStack {
                            Text("\(stats.perfectCount)")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(.yellow)
                            Text("PERFECT")
                                .font(.caption)
                                .foregroundColor(.white.opacity(0.7))
                        }
                        
                        VStack {
                            Text("\(stats.goodCount)")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(.green)
                            Text("GOOD")
                                .font(.caption)
                                .foregroundColor(.white.opacity(0.7))
                        }
                        
                        VStack {
                            Text("\(stats.missCount)")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(.red)
                            Text("MISS")
                                .font(.caption)
                                .foregroundColor(.white.opacity(0.7))
                        }
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color.white.opacity(0.1))
                )
                
                // Buttons
                VStack(spacing: 15) {
                    Button(action: onRestart) {
                        HStack {
                            Image(systemName: "arrow.clockwise")
                            Text("PLAY AGAIN")
                                .fontWeight(.bold)
                        }
                        .font(.title2)
                        .foregroundColor(.white)
                        .padding(.horizontal, 40)
                        .padding(.vertical, 15)
                        .background(
                            RoundedRectangle(cornerRadius: 25)
                                .fill(Color.green)
                        )
                    }
                    
                    Button(action: onMainMenu) {
                        HStack {
                            Image(systemName: "house")
                            Text("MAIN MENU")
                                .fontWeight(.bold)
                        }
                        .font(.title2)
                        .foregroundColor(.white)
                        .padding(.horizontal, 40)
                        .padding(.vertical, 15)
                        .background(
                            RoundedRectangle(cornerRadius: 25)
                                .fill(Color.blue)
                        )
                    }
                }
            }
            .padding()
        }
    }
    
    private func getGrade() -> (String, Color) {
        let accuracy = stats.accuracy
        
        if accuracy >= 95 {
            return ("S", .yellow)
        } else if accuracy >= 90 {
            return ("A", .green)
        } else if accuracy >= 80 {
            return ("B", .blue)
        } else if accuracy >= 70 {
            return ("C", .orange)
        } else {
            return ("D", .red)
        }
    }
}

#Preview {
    GameOverView(
        stats: GameStats(
            score: 12500,
            combo: 0,
            maxCombo: 45,
            perfectCount: 32,
            goodCount: 8,
            missCount: 5
        ),
        onRestart: {},
        onMainMenu: {}
    )
}
