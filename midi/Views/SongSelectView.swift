//
//  SongSelectView.swift
//  midi
//
//  Created by lincoo on 7/14/25.
//

import SwiftUI

struct SongSelectView: View {
    @StateObject private var songManager = SongManager()
    @State private var selectedSong: Song?
    @Binding var isPresented: Bool
    
    let onSongSelected: (Song) -> Void
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background
                LinearGradient(
                    colors: [Color.black, Color.purple.opacity(0.3)],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
                
                VStack {
                    // Header
                    Text("Select Song")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .padding()
                    
                    // Song List
                    ScrollView {
                        LazyVStack(spacing: 12) {
                            ForEach(songManager.availableSongs) { song in
                                SongCard(
                                    song: song,
                                    isSelected: selectedSong?.id == song.id
                                ) {
                                    selectedSong = song
                                }
                            }
                        }
                        .padding()
                    }
                    
                    // Bottom Controls
                    HStack {
                        Button("Cancel") {
                            isPresented = false
                        }
                        .font(.title2)
                        .foregroundColor(.white)
                        .padding(.horizontal, 30)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(Color.gray.opacity(0.6))
                        )
                        
                        Spacer()
                        
                        Button("Play") {
                            if let song = selectedSong {
                                onSongSelected(song)
                                isPresented = false
                            }
                        }
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .padding(.horizontal, 30)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(selectedSong != nil ? Color.green : Color.gray.opacity(0.6))
                        )
                        .disabled(selectedSong == nil)
                    }
                    .padding()
                }
            }
        }
    }
}

struct SongCard: View {
    let song: Song
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    Text(song.title)
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    Text(song.artist)
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.8))
                    
                    HStack {
                        Text("\(Int(song.bpm)) BPM")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.7))
                        
                        Spacer()
                        
                        Text(song.difficulty.rawValue)
                            .font(.caption)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(song.difficulty.color)
                            )
                    }
                }
                
                Spacer()
                
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.title)
                        .foregroundColor(.green)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 15)
                    .fill(isSelected ? Color.blue.opacity(0.3) : Color.white.opacity(0.1))
                    .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

class SongManager: ObservableObject {
    @Published var availableSongs: [Song] = []
    
    init() {
        loadSongs()
    }
    
    private func loadSongs() {
        // Create demo songs
        availableSongs = [
            Song(
                title: "C Major Scale",
                artist: "Demo",
                bpm: 120,
                duration: 30,
                audioFileName: "demo_c_major.mp3",
                chartFileName: "demo_c_major.chart",
                difficulty: .easy
            ),
            Song(
                title: "Chromatic Run",
                artist: "Demo",
                bpm: 140,
                duration: 45,
                audioFileName: "demo_chromatic.mp3",
                chartFileName: "demo_chromatic.chart",
                difficulty: .normal
            ),
            Song(
                title: "Arpeggio Challenge",
                artist: "Demo",
                bpm: 160,
                duration: 60,
                audioFileName: "demo_arpeggio.mp3",
                chartFileName: "demo_arpeggio.chart",
                difficulty: .hard
            ),
            Song(
                title: "Polyrhythm Master",
                artist: "Demo",
                bpm: 180,
                duration: 90,
                audioFileName: "demo_polyrhythm.mp3",
                chartFileName: "demo_polyrhythm.chart",
                difficulty: .expert
            )
        ]
    }
    
    func loadChart(for song: Song) -> Chart? {
        // In a real implementation, this would load from a file
        // For now, generate charts programmatically
        return generateChart(for: song)
    }
    
    private func generateChart(for song: Song) -> Chart {
        var notes: [Note] = []
        let beatInterval = 60.0 / song.bpm
        
        switch song.difficulty {
        case .easy:
            // Simple C major scale pattern
            let pattern = [0, 2, 4, 5, 7, 9, 11, 12]
            for beat in 0..<Int(song.duration / beatInterval) {
                if beat % 2 == 0 { // Every other beat
                    let keyIndex = pattern[beat % pattern.count]
                    notes.append(Note(keyIndex: keyIndex, startTime: Double(beat) * beatInterval, duration: nil))
                }
            }
            
        case .normal:
            // Chromatic pattern with some rhythm
            for beat in 0..<Int(song.duration / beatInterval) {
                if beat % 4 != 3 { // Skip every 4th beat
                    let keyIndex = beat % 25
                    notes.append(Note(keyIndex: keyIndex, startTime: Double(beat) * beatInterval, duration: nil))
                }
            }
            
        case .hard:
            // Arpeggio patterns
            let arpeggios = [
                [0, 4, 7, 12], // C major
                [2, 6, 9, 14], // D minor
                [4, 8, 11, 16], // E minor
                [5, 9, 12, 17]  // F major
            ]
            
            for beat in 0..<Int(song.duration / beatInterval) {
                let arpeggio = arpeggios[beat / 4 % arpeggios.count]
                let keyIndex = arpeggio[beat % arpeggio.count]
                if keyIndex < 25 {
                    notes.append(Note(keyIndex: keyIndex, startTime: Double(beat) * beatInterval, duration: nil))
                }
            }
            
        case .expert:
            // Complex polyrhythmic patterns
            let totalBeats = Int(song.duration / beatInterval)
            
            // Main melody (quarter notes)
            for beat in stride(from: 0, to: totalBeats, by: 1) {
                let keyIndex = (beat * 3) % 25
                notes.append(Note(keyIndex: keyIndex, startTime: Double(beat) * beatInterval, duration: nil))
            }

            // Counter melody (eighth notes, offset)
            for beat in stride(from: 0, to: totalBeats * 2, by: 1) {
                if beat % 3 == 0 {
                    let keyIndex = (beat * 2 + 12) % 25
                    notes.append(Note(keyIndex: keyIndex, startTime: Double(beat) * beatInterval * 0.5, duration: nil))
                }
            }
        }
        
        return Chart(notes: notes, bpm: song.bpm)
    }
}

#Preview {
    SongSelectView(isPresented: .constant(true)) { song in
        print("Selected song: \(song.title)")
    }
}
