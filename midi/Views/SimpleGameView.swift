//
//  SimpleGameView.swift
//  midi
//
//  Created by lincoo on 7/14/25.
//

import SwiftUI

struct SimpleGameView: View {
    @State private var score = 0
    @State private var combo = 0
    @State private var notes: [SimpleNote] = []
    @State private var pressedKeys: Set<Int> = []
    @Environment(\.dismiss) private var dismiss
    
    let keyWidth: CGFloat = 40
    let keyHeight: CGFloat = 120
    let trackHeight: CGFloat = 600
    
    var body: some View {
        ZStack {
            // Background
            LinearGradient(
                colors: [Color.black, Color.blue.opacity(0.3)],
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea()
            
            VStack(spacing: 0) {
                // Top UI
                topUI
                    .padding()
                
                // Game Area
                gameArea
                
                // Keyboard
                keyboardView
                    .padding(.bottom)
            }
        }
        .onAppear {
            startDemo()
        }
    }
    
    private var topUI: some View {
        HStack {
            // Back button
            Button(action: {
                dismiss()
            }) {
                Image(systemName: "xmark")
                    .font(.title2)
                    .foregroundColor(.white)
            }
            
            Spacer()
            
            // Score
            VStack(alignment: .leading) {
                Text("SCORE")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                Text("\(score)")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
            }
            
            Spacer()
            
            // Combo
            VStack {
                Text("COMBO")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                Text("\(combo)")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.yellow)
            }
            
            Spacer()
        }
    }
    
    private var gameArea: some View {
        ZStack {
            // Note tracks
            HStack(spacing: 2) {
                ForEach(0..<25, id: \.self) { keyIndex in
                    noteTrack(for: keyIndex)
                }
            }
            .frame(height: trackHeight)
            
            // Hit line
            Rectangle()
                .fill(Color.white.opacity(0.8))
                .frame(height: 3)
                .offset(y: trackHeight/2 - 60)
        }
    }
    
    private func noteTrack(for keyIndex: Int) -> some View {
        ZStack {
            // Track background
            Rectangle()
                .fill(isBlackKey(keyIndex) ? Color.gray.opacity(0.3) : Color.white.opacity(0.1))
                .frame(width: keyWidth)
            
            // Track border
            Rectangle()
                .stroke(Color.white.opacity(0.3), lineWidth: 1)
                .frame(width: keyWidth)
            
            // Notes in this track
            ForEach(notes.filter { $0.keyIndex == keyIndex }) { note in
                noteView(note)
            }
            
            // Hit effect
            if pressedKeys.contains(keyIndex) {
                Rectangle()
                    .fill(Color.yellow.opacity(0.5))
                    .frame(width: keyWidth, height: 60)
                    .offset(y: trackHeight/2 - 60)
            }
        }
    }
    
    private func noteView(_ note: SimpleNote) -> some View {
        Rectangle()
            .fill(isBlackKey(note.keyIndex) ? Color.purple : Color.cyan)
            .frame(width: keyWidth - 4, height: 20)
            .offset(y: note.currentY - trackHeight/2)
            .shadow(color: .white.opacity(0.5), radius: 2)
    }
    
    private var keyboardView: some View {
        HStack(spacing: 2) {
            ForEach(0..<25, id: \.self) { keyIndex in
                keyView(for: keyIndex)
            }
        }
        .padding(.horizontal)
    }
    
    private func keyView(for keyIndex: Int) -> some View {
        let isPressed = pressedKeys.contains(keyIndex)
        let isBlack = isBlackKey(keyIndex)
        
        return Button(action: {
            handleKeyPress(keyIndex)
        }) {
            Rectangle()
                .fill(isPressed ? Color.yellow : (isBlack ? Color.black : Color.white))
                .frame(width: keyWidth, height: keyHeight)
                .overlay(
                    Rectangle()
                        .stroke(Color.gray, lineWidth: 1)
                )
                .overlay(
                    VStack {
                        Spacer()
                        Text(keyName(for: keyIndex))
                            .font(.caption2)
                            .foregroundColor(isBlack ? .white : .black)
                            .padding(.bottom, 4)
                    }
                )
                .scaleEffect(isPressed ? 0.95 : 1.0)
                .animation(.easeInOut(duration: 0.1), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func isBlackKey(_ keyIndex: Int) -> Bool {
        let noteInOctave = keyIndex % 12
        return [1, 3, 6, 8, 10].contains(noteInOctave)
    }
    
    private func keyName(for keyIndex: Int) -> String {
        let noteNames = ["C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"]
        let baseNote = 48 // C3
        let octave = (keyIndex + baseNote) / 12
        let noteIndex = (keyIndex + baseNote) % 12
        return "\(noteNames[noteIndex])\(octave)"
    }
    
    private func handleKeyPress(_ keyIndex: Int) {
        pressedKeys.insert(keyIndex)
        
        // Check for hits
        let hitZone = trackHeight/2 - 60
        let hitWindow: CGFloat = 30
        
        for (index, note) in notes.enumerated() {
            if note.keyIndex == keyIndex && abs(note.currentY - hitZone) <= hitWindow {
                // Hit!
                score += 100
                combo += 1
                notes.remove(at: index)
                break
            }
        }
        
        // Remove key press after a short delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            pressedKeys.remove(keyIndex)
        }
    }
    
    private func startDemo() {
        // Create some demo notes
        let pattern = [0, 2, 4, 7, 9, 12, 14, 16, 19, 21, 24]
        
        for i in 0..<20 {
            let keyIndex = pattern[i % pattern.count]
            let note = SimpleNote(
                keyIndex: keyIndex,
                currentY: -trackHeight/2 - CGFloat(i * 100)
            )
            notes.append(note)
        }
        
        // Start animation
        startNoteAnimation()
    }
    
    private func startNoteAnimation() {
        Timer.scheduledTimer(withTimeInterval: 1.0/60.0, repeats: true) { timer in
            for i in 0..<notes.count {
                notes[i].currentY += 2.0
            }
            
            // Remove notes that have gone off screen
            notes.removeAll { note in
                note.currentY > trackHeight/2 + 100
            }
            
            // Stop timer if no notes left
            if notes.isEmpty {
                timer.invalidate()
            }
        }
    }
}

struct SimpleNote: Identifiable {
    let id = UUID()
    let keyIndex: Int
    var currentY: CGFloat
}

#Preview {
    SimpleGameView()
}
