//
//  SimpleGameView.swift
//  midi
//
//  Created by lincoo on 7/14/25.
//

import SwiftUI

class GameController: ObservableObject {
    @Published var score = 0
    @Published var combo = 0
    @Published var notes: [SimpleNote] = []
    @Published var pressedKeys: Set<Int> = []

    let midiManager = MIDIManager()

    init() {
        midiManager.delegate = self
    }

    func checkForHit(keyIndex: Int) {
        // Check for hits
        let hitZone: CGFloat = 300 - 60 // trackHeight/2 - 60
        let hitWindow: CGFloat = 30

        for (index, note) in notes.enumerated() {
            if note.keyIndex == keyIndex && abs(note.currentY - hitZone) <= hitWindow {
                // Hit!
                score += 100
                combo += 1
                notes.remove(at: index)
                print("Hit! Key: \(keyIndex), Score: \(score), Combo: \(combo)")
                break
            }
        }
    }
}

extension GameController: MIDIManagerDelegate {
    func midiManager(_ manager: <PERSON><PERSON><PERSON><PERSON><PERSON>, didReceiveNoteOn note: UInt8, velocity: UInt8) {
        // Convert MIDI note to key index
        let baseNote: UInt8 = 48 // C3
        let keyIndex = Int(note - baseNote)

        // Check if it's within our 25-key range
        guard keyIndex >= 0 && keyIndex < 25 else { return }

        print("MIDI Note On: \(note) -> Key Index: \(keyIndex)")
        DispatchQueue.main.async {
            self.checkForHit(keyIndex: keyIndex)
        }
    }

    func midiManager(_ manager: MIDIManager, didReceiveNoteOff note: UInt8) {
        // Note off handling if needed
        let baseNote: UInt8 = 48 // C3
        let keyIndex = Int(note - baseNote)
        print("MIDI Note Off: \(note) -> Key Index: \(keyIndex)")
    }

    func midiManager(_ manager: MIDIManager, didConnectDevice deviceName: String) {
        print("MIDI Device Connected: \(deviceName)")
    }

    func midiManager(_ manager: MIDIManager, didDisconnectDevice deviceName: String) {
        print("MIDI Device Disconnected: \(deviceName)")
    }
}

struct SimpleGameView: View {
    @StateObject private var gameController = GameController()
    @Environment(\.dismiss) private var dismiss
    
    let keyWidth: CGFloat = 40
    let keyHeight: CGFloat = 120
    let trackHeight: CGFloat = 600
    
    var body: some View {
        ZStack {
            // Background
            LinearGradient(
                colors: [Color.black, Color.blue.opacity(0.3)],
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea()
            
            VStack(spacing: 0) {
                // Top UI
                topUI
                    .padding()

                // MIDI Status
                midiStatusView
                    .padding(.horizontal)

                // Game Area
                gameArea

                // Keyboard
                keyboardView
                    .padding(.bottom)
            }
        }
        .onAppear {
            startDemo()
        }
    }
    
    private var topUI: some View {
        HStack {
            // Back button
            Button(action: {
                dismiss()
            }) {
                Image(systemName: "xmark")
                    .font(.title2)
                    .foregroundColor(.white)
            }

            Spacer()

            // Score
            VStack(alignment: .leading) {
                Text("SCORE")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                Text("\(gameController.score)")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
            }

            Spacer()

            // Combo
            VStack {
                Text("COMBO")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                Text("\(gameController.combo)")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.yellow)
            }

            Spacer()
        }
    }

    private var midiStatusView: some View {
        HStack {
            // MIDI Status Indicator
            HStack(spacing: 8) {
                Circle()
                    .fill(gameController.midiManager.isConnected ? Color.green : Color.red)
                    .frame(width: 8, height: 8)

                Text(gameController.midiManager.isConnected ? "MIDI Connected" : "MIDI Disconnected")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
            }

            Spacer()

            // Connected Devices
            if !gameController.midiManager.connectedDevices.isEmpty {
                Text("Devices: \(gameController.midiManager.connectedDevices.joined(separator: ", "))")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.6))
            } else if let error = gameController.midiManager.lastError {
                Text("Error: \(error)")
                    .font(.caption)
                    .foregroundColor(.red.opacity(0.8))
            }
        }
        .padding(.vertical, 4)
        .padding(.horizontal, 12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.black.opacity(0.3))
        )
    }
    
    private var gameArea: some View {
        ZStack {
            // Note tracks
            HStack(spacing: 2) {
                ForEach(0..<25, id: \.self) { keyIndex in
                    noteTrack(for: keyIndex)
                }
            }
            .frame(height: trackHeight)
            
            // Hit line
            Rectangle()
                .fill(Color.white.opacity(0.8))
                .frame(height: 3)
                .offset(y: trackHeight/2 - 60)
        }
    }
    
    private func noteTrack(for keyIndex: Int) -> some View {
        ZStack {
            // Track background
            Rectangle()
                .fill(isBlackKey(keyIndex) ? Color.gray.opacity(0.3) : Color.white.opacity(0.1))
                .frame(width: keyWidth)
            
            // Track border
            Rectangle()
                .stroke(Color.white.opacity(0.3), lineWidth: 1)
                .frame(width: keyWidth)
            
            // Notes in this track
            ForEach(gameController.notes.filter { $0.keyIndex == keyIndex }) { note in
                noteView(note)
            }

            // Hit effect (from mouse clicks or MIDI)
            if gameController.pressedKeys.contains(keyIndex) || gameController.midiManager.pressedKeys.contains(keyIndex) {
                Rectangle()
                    .fill(Color.yellow.opacity(0.5))
                    .frame(width: keyWidth, height: 60)
                    .offset(y: trackHeight/2 - 60)
            }
        }
    }
    
    private func noteView(_ note: SimpleNote) -> some View {
        Rectangle()
            .fill(isBlackKey(note.keyIndex) ? Color.purple : Color.cyan)
            .frame(width: keyWidth - 4, height: 20)
            .offset(y: note.currentY - trackHeight/2)
            .shadow(color: .white.opacity(0.5), radius: 2)
    }
    
    private var keyboardView: some View {
        HStack(spacing: 2) {
            ForEach(0..<25, id: \.self) { keyIndex in
                keyView(for: keyIndex)
            }
        }
        .padding(.horizontal)
    }
    
    private func keyView(for keyIndex: Int) -> some View {
        let isPressed = gameController.pressedKeys.contains(keyIndex) || gameController.midiManager.pressedKeys.contains(keyIndex)
        let isBlack = isBlackKey(keyIndex)

        return Button(action: {
            handleKeyPress(keyIndex)
        }) {
            Rectangle()
                .fill(isPressed ? Color.yellow : (isBlack ? Color.black : Color.white))
                .frame(width: keyWidth, height: keyHeight)
                .overlay(
                    Rectangle()
                        .stroke(Color.gray, lineWidth: 1)
                )
                .overlay(
                    VStack {
                        Spacer()
                        Text(keyName(for: keyIndex))
                            .font(.caption2)
                            .foregroundColor(isBlack ? .white : .black)
                            .padding(.bottom, 4)
                    }
                )
                .scaleEffect(isPressed ? 0.95 : 1.0)
                .animation(.easeInOut(duration: 0.1), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func isBlackKey(_ keyIndex: Int) -> Bool {
        let noteInOctave = keyIndex % 12
        return [1, 3, 6, 8, 10].contains(noteInOctave)
    }
    
    private func keyName(for keyIndex: Int) -> String {
        let noteNames = ["C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"]
        let baseNote = 48 // C3
        let octave = (keyIndex + baseNote) / 12
        let noteIndex = (keyIndex + baseNote) % 12
        return "\(noteNames[noteIndex])\(octave)"
    }
    
    private func handleKeyPress(_ keyIndex: Int) {
        gameController.pressedKeys.insert(keyIndex)
        gameController.checkForHit(keyIndex: keyIndex)

        // Remove key press after a short delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            gameController.pressedKeys.remove(keyIndex)
        }
    }
    
    private func startDemo() {
        // Create some demo notes
        let pattern = [0, 2, 4, 7, 9, 12, 14, 16, 19, 21, 24]
        
        for i in 0..<20 {
            let keyIndex = pattern[i % pattern.count]
            let note = SimpleNote(
                keyIndex: keyIndex,
                currentY: -trackHeight/2 - CGFloat(i * 100)
            )
            gameController.notes.append(note)
        }
        
        // Start animation
        startNoteAnimation()
    }
    
    private func startNoteAnimation() {
        Timer.scheduledTimer(withTimeInterval: 1.0/60.0, repeats: true) { timer in
            for i in 0..<gameController.notes.count {
                gameController.notes[i].currentY += 2.0
            }

            // Remove notes that have gone off screen
            gameController.notes.removeAll { note in
                note.currentY > trackHeight/2 + 100
            }

            // Stop timer if no notes left
            if gameController.notes.isEmpty {
                timer.invalidate()
            }
        }
    }
}

struct SimpleNote: Identifiable {
    let id = UUID()
    let keyIndex: Int
    var currentY: CGFloat
}



#Preview {
    SimpleGameView()
}
