//
//  GameModels.swift
//  midi
//
//  Created by lincoo on 7/14/25.
//

import Foundation
import SwiftUI

// MARK: - Game State
enum GameState {
    case menu
    case playing
    case paused
    case gameOver
    case songSelect
}

// MARK: - Note Models
struct Note: Identifiable, Equatable {
    let id = UUID()
    let keyIndex: Int // 0-24 for 25 keys
    let startTime: TimeInterval // When the note should be hit
    var currentY: CGFloat = 0 // Current position on screen
    let duration: TimeInterval? // For long notes (optional)
    
    static func == (lhs: Note, rhs: Note) -> Bool {
        lhs.id == rhs.id
    }
}

// MARK: - Hit Judgment
enum HitJudgment {
    case perfect
    case good
    case miss
    
    var score: Int {
        switch self {
        case .perfect: return 300
        case .good: return 100
        case .miss: return 0
        }
    }
    
    var color: Color {
        switch self {
        case .perfect: return .yellow
        case .good: return .green
        case .miss: return .red
        }
    }
    
    var text: String {
        switch self {
        case .perfect: return "PERFECT"
        case .good: return "GOOD"
        case .miss: return "MISS"
        }
    }
}

// MARK: - Game Statistics
struct GameStats {
    var score: Int = 0
    var combo: Int = 0
    var maxCombo: Int = 0
    var perfectCount: Int = 0
    var goodCount: Int = 0
    var missCount: Int = 0
    var accuracy: Double {
        let total = perfectCount + goodCount + missCount
        guard total > 0 else { return 0 }
        return Double(perfectCount * 100 + goodCount * 50) / Double(total * 100)
    }
}

// MARK: - Song Data
struct Song: Identifiable {
    let id = UUID()
    let title: String
    let artist: String
    let bpm: Double
    let duration: TimeInterval
    let audioFileName: String
    let chartFileName: String
    let difficulty: Difficulty
    
    enum Difficulty: String, CaseIterable {
        case easy = "Easy"
        case normal = "Normal"
        case hard = "Hard"
        case expert = "Expert"
        
        var color: Color {
            switch self {
            case .easy: return .green
            case .normal: return .blue
            case .hard: return .orange
            case .expert: return .red
            }
        }
    }
}

// MARK: - Chart Data
struct Chart {
    let notes: [Note]
    let bpm: Double
    let offset: TimeInterval // Audio offset
    
    init(notes: [Note], bpm: Double, offset: TimeInterval = 0) {
        self.notes = notes.sorted { $0.startTime < $1.startTime }
        self.bpm = bpm
        self.offset = offset
    }
}

// MARK: - MIDI Key Mapping
struct MIDIKeyMapping {
    static let keyCount = 25
    static let baseNote: UInt8 = 48 // C3
    
    static func keyIndex(for midiNote: UInt8) -> Int? {
        let index = Int(midiNote - baseNote)
        return (0..<keyCount).contains(index) ? index : nil
    }
    
    static func midiNote(for keyIndex: Int) -> UInt8? {
        guard (0..<keyCount).contains(keyIndex) else { return nil }
        return baseNote + UInt8(keyIndex)
    }
    
    static func keyName(for keyIndex: Int) -> String {
        let noteNames = ["C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"]
        let octave = (keyIndex + Int(baseNote)) / 12
        let noteIndex = (keyIndex + Int(baseNote)) % 12
        return "\(noteNames[noteIndex])\(octave)"
    }
}

// MARK: - Game Settings
struct GameSettings {
    var noteSpeed: Double = 1.0 // Multiplier for note falling speed
    var hitWindow: TimeInterval = 0.1 // Time window for perfect hit
    var goodWindow: TimeInterval = 0.2 // Time window for good hit
    var masterVolume: Float = 1.0
    var musicVolume: Float = 0.8
    var effectVolume: Float = 1.0
    var showKeyboard: Bool = true
    var showFPS: Bool = false
}
