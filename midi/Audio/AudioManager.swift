//
//  AudioManager.swift
//  midi
//
//  Created by lincoo on 7/14/25.
//

import Foundation
import AVFoundation
import AudioToolbox

class AudioManager: ObservableObject {
    private var audioEngine = AVAudioEngine()
    private var musicPlayer: AVAudioPlayerNode?
    private var effectPlayers: [AVAudioPlayerNode] = []
    private var mixer: AVAudioMixerNode?
    
    @Published var isMusicPlaying = false
    @Published var musicVolume: Float = 0.8 {
        didSet {
            updateMusicVolume()
        }
    }
    @Published var effectVolume: Float = 1.0 {
        didSet {
            updateEffectVolume()
        }
    }
    
    private var hitSounds: [AVAudioPCMBuffer] = []
    private var currentMusicFile: AVAudioFile?
    
    init() {
        // Delay audio setup to avoid initialization crashes
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.setupAudioEngine()
            self.loadHitSounds()
        }
    }
    
    deinit {
        audioEngine.stop()
    }
    
    // MARK: - Audio Engine Setup
    private func setupAudioEngine() {
        do {
            mixer = audioEngine.mainMixerNode

            // Create music player
            musicPlayer = AVAudioPlayerNode()
            if let musicPlayer = musicPlayer {
                audioEngine.attach(musicPlayer)
                audioEngine.connect(musicPlayer, to: mixer!, format: nil)
            }

            // Create effect players
            for _ in 0..<5 { // Reduced pool to 5 effect players
                let player = AVAudioPlayerNode()
                audioEngine.attach(player)
                audioEngine.connect(player, to: mixer!, format: nil)
                effectPlayers.append(player)
            }

            try audioEngine.start()
            print("Audio engine started successfully")
        } catch {
            print("Failed to start audio engine: \(error)")
            // Continue without audio if setup fails
        }
    }
    
    private func loadHitSounds() {
        // Generate simple hit sounds programmatically
        hitSounds = generateHitSounds()
    }
    
    private func generateHitSounds() -> [AVAudioPCMBuffer] {
        var sounds: [AVAudioPCMBuffer] = []
        
        let sampleRate: Double = 44100
        let duration: Double = 0.1
        let frameCount = AVAudioFrameCount(sampleRate * duration)
        
        guard let format = AVAudioFormat(standardFormatWithSampleRate: sampleRate, channels: 1) else {
            return sounds
        }
        
        // Perfect hit sound (higher pitch)
        if let perfectBuffer = AVAudioPCMBuffer(pcmFormat: format, frameCapacity: frameCount) {
            perfectBuffer.frameLength = frameCount
            generateTone(buffer: perfectBuffer, frequency: 800, amplitude: 0.3)
            sounds.append(perfectBuffer)
        }
        
        // Good hit sound (medium pitch)
        if let goodBuffer = AVAudioPCMBuffer(pcmFormat: format, frameCapacity: frameCount) {
            goodBuffer.frameLength = frameCount
            generateTone(buffer: goodBuffer, frequency: 600, amplitude: 0.25)
            sounds.append(goodBuffer)
        }
        
        // Miss sound (lower pitch, different waveform)
        if let missBuffer = AVAudioPCMBuffer(pcmFormat: format, frameCapacity: frameCount) {
            missBuffer.frameLength = frameCount
            generateNoise(buffer: missBuffer, amplitude: 0.1)
            sounds.append(missBuffer)
        }
        
        return sounds
    }
    
    private func generateTone(buffer: AVAudioPCMBuffer, frequency: Double, amplitude: Float) {
        let sampleRate = buffer.format.sampleRate
        let frameCount = Int(buffer.frameLength)
        
        guard let channelData = buffer.floatChannelData?[0] else { return }
        
        for frame in 0..<frameCount {
            let time = Double(frame) / sampleRate
            let value = sin(2.0 * Double.pi * frequency * time) * Double(amplitude)
            
            // Apply envelope to avoid clicks
            let envelope = sin(Double.pi * time / (Double(frameCount) / sampleRate))
            channelData[frame] = Float(value * envelope)
        }
    }
    
    private func generateNoise(buffer: AVAudioPCMBuffer, amplitude: Float) {
        let frameCount = Int(buffer.frameLength)
        guard let channelData = buffer.floatChannelData?[0] else { return }
        
        for frame in 0..<frameCount {
            let value = Float.random(in: -1...1) * amplitude
            
            // Apply envelope
            let envelope = 1.0 - Float(frame) / Float(frameCount)
            channelData[frame] = value * envelope
        }
    }
    
    // MARK: - Music Playback
    func playBackgroundMusic(fileName: String) {
        guard let musicPlayer = musicPlayer else { return }
        
        // Stop current music if playing
        if musicPlayer.isPlaying {
            musicPlayer.stop()
        }
        
        // Load music file
        guard let url = Bundle.main.url(forResource: fileName, withExtension: nil) else {
            print("Could not find music file: \(fileName)")
            return
        }
        
        do {
            currentMusicFile = try AVAudioFile(forReading: url)
            guard let musicFile = currentMusicFile else { return }
            
            musicPlayer.scheduleFile(musicFile, at: nil) {
                DispatchQueue.main.async {
                    self.isMusicPlaying = false
                }
            }
            
            musicPlayer.play()
            isMusicPlaying = true
            
        } catch {
            print("Failed to load music file: \(error)")
        }
    }
    
    func stopBackgroundMusic() {
        musicPlayer?.stop()
        isMusicPlaying = false
    }
    
    func pauseBackgroundMusic() {
        musicPlayer?.pause()
        isMusicPlaying = false
    }
    
    func resumeBackgroundMusic() {
        musicPlayer?.play()
        isMusicPlaying = true
    }
    
    // MARK: - Sound Effects
    func playHitSound(for judgment: HitJudgment) {
        let soundIndex: Int
        switch judgment {
        case .perfect: soundIndex = 0
        case .good: soundIndex = 1
        case .miss: soundIndex = 2
        }
        
        guard soundIndex < hitSounds.count else { return }
        playEffect(buffer: hitSounds[soundIndex])
    }
    
    func playKeySound(for keyIndex: Int) {
        // Generate a simple tone based on key index
        let baseFrequency = 261.63 // C4
        let frequency = baseFrequency * pow(2.0, Double(keyIndex) / 12.0)
        
        playTone(frequency: frequency, duration: 0.1, amplitude: 0.2)
    }
    
    private func playEffect(buffer: AVAudioPCMBuffer) {
        // Find an available effect player
        for player in effectPlayers {
            if !player.isPlaying {
                player.scheduleBuffer(buffer, at: nil, options: [], completionHandler: nil)
                player.play()
                break
            }
        }
    }
    
    private func playTone(frequency: Double, duration: Double, amplitude: Float) {
        let sampleRate: Double = 44100
        let frameCount = AVAudioFrameCount(sampleRate * duration)
        
        guard let format = AVAudioFormat(standardFormatWithSampleRate: sampleRate, channels: 1),
              let buffer = AVAudioPCMBuffer(pcmFormat: format, frameCapacity: frameCount) else {
            return
        }
        
        buffer.frameLength = frameCount
        generateTone(buffer: buffer, frequency: frequency, amplitude: amplitude)
        playEffect(buffer: buffer)
    }
    
    // MARK: - Volume Control
    private func updateMusicVolume() {
        musicPlayer?.volume = musicVolume
    }
    
    private func updateEffectVolume() {
        for player in effectPlayers {
            player.volume = effectVolume
        }
    }
    
    // MARK: - Metronome
    func startMetronome(bpm: Double) {
        // Implementation for metronome if needed
    }
    
    func stopMetronome() {
        // Implementation for metronome if needed
    }
}
