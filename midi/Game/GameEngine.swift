//
//  GameEngine.swift
//  midi
//
//  Created by lincoo on 7/14/25.
//

import Foundation
import SwiftUI
import Combine

class GameEngine: ObservableObject, MIDIManagerDelegate {
    @Published var gameState: GameState = .menu
    @Published var stats = GameStats()
    @Published var activeNotes: [Note] = []
    @Published var lastJudgment: HitJudgment?
    @Published var settings = GameSettings()

    weak var midiManager: MIDIManager?
    private let audioManager = AudioManager()
    
    private var currentChart: Chart?
    private var gameTimer: Timer?
    private var startTime: Date?
    private var noteSpawnTimer: Timer?
    private var judgmentTimer: Timer?
    
    private let trackHeight: CGFloat = 600
    private let hitLineOffset: CGFloat = 60
    private let noteSpeed: CGFloat = 200 // pixels per second
    
    init() {
        // Initialize with minimal setup
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            self.setupTimers()
        }
    }
    
    deinit {
        stopGame()
    }
    
    // MARK: - Game Control
    func startGame(with chart: Chart) {
        currentChart = chart
        resetGameState()
        gameState = .playing
        startTime = Date()
        
        startGameLoop()
        startNoteSpawning()
    }
    
    func startGame(with song: Song) {
        let songManager = SongManager()
        if let chart = songManager.loadChart(for: song) {
            startGame(with: chart)
        } else {
            // Fallback to demo
            startDemoSong()
        }
    }

    func startDemoSong() {
        let demoChart = createDemoChart()
        startGame(with: demoChart)
    }
    
    func pauseGame() {
        guard gameState == .playing else { return }
        gameState = .paused
        stopTimers()
    }
    
    func resumeGame() {
        guard gameState == .paused else { return }
        gameState = .playing
        startGameLoop()
        startNoteSpawning()
    }
    
    func stopGame() {
        gameState = .gameOver
        stopTimers()
    }
    
    private func resetGameState() {
        stats = GameStats()
        activeNotes.removeAll()
        lastJudgment = nil
    }
    
    // MARK: - Game Loop
    func setupTimers() {
        // Clear judgment display
        judgmentTimer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { _ in
            self.lastJudgment = nil
        }
    }

    private func setupGameTimers() {
        setupTimers()
    }
    
    private func startGameLoop() {
        gameTimer?.invalidate()
        gameTimer = Timer.scheduledTimer(withTimeInterval: 1.0/60.0, repeats: true) { _ in
            self.updateGame()
        }
    }
    
    private func startNoteSpawning() {
        noteSpawnTimer?.invalidate()
        noteSpawnTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { _ in
            self.spawnNotes()
        }
    }
    
    private func stopTimers() {
        gameTimer?.invalidate()
        noteSpawnTimer?.invalidate()
    }
    
    private func updateGame() {
        guard gameState == .playing else { return }
        
        let deltaTime = 1.0/60.0
        updateNotePositions(deltaTime: deltaTime)
        checkMissedNotes()
    }
    
    private func updateNotePositions(deltaTime: TimeInterval) {
        for i in 0..<activeNotes.count {
            activeNotes[i].currentY += CGFloat(noteSpeed * deltaTime * settings.noteSpeed)
        }
        
        // Remove notes that have gone off screen
        activeNotes.removeAll { note in
            note.currentY > trackHeight/2 + 100
        }
    }
    
    private func spawnNotes() {
        guard let chart = currentChart,
              let startTime = startTime else { return }
        
        let currentTime = Date().timeIntervalSince(startTime)
        let spawnTime = currentTime + 3.0 // Spawn notes 3 seconds ahead
        
        let notesToSpawn = chart.notes.filter { note in
            abs(note.startTime - spawnTime) < 0.1 && !activeNotes.contains(note)
        }
        
        for note in notesToSpawn {
            var spawnedNote = note
            spawnedNote.currentY = -trackHeight/2
            activeNotes.append(spawnedNote)
        }
    }
    
    private func checkMissedNotes() {
        let missThreshold = trackHeight/2 - hitLineOffset + 50
        
        let missedNotes = activeNotes.filter { note in
            note.currentY > missThreshold
        }
        
        for note in missedNotes {
            handleMiss()
            activeNotes.removeAll { $0.id == note.id }
        }
    }
    
    // MARK: - Hit Detection
    private func checkHit(for keyIndex: Int) -> HitJudgment? {
        let hitZone = trackHeight/2 - hitLineOffset
        let perfectWindow: CGFloat = 20
        let goodWindow: CGFloat = 40
        
        let notesInTrack = activeNotes.filter { $0.keyIndex == keyIndex }
        
        for note in notesInTrack {
            let distance = abs(note.currentY - hitZone)
            
            if distance <= perfectWindow {
                activeNotes.removeAll { $0.id == note.id }
                return .perfect
            } else if distance <= goodWindow {
                activeNotes.removeAll { $0.id == note.id }
                return .good
            }
        }
        
        return nil
    }
    
    private func handleHit(_ judgment: HitJudgment) {
        lastJudgment = judgment
        stats.score += judgment.score

        // Play hit sound
        audioManager.playHitSound(for: judgment)

        switch judgment {
        case .perfect:
            stats.perfectCount += 1
            stats.combo += 1
        case .good:
            stats.goodCount += 1
            stats.combo += 1
        case .miss:
            stats.missCount += 1
            stats.combo = 0
        }

        stats.maxCombo = max(stats.maxCombo, stats.combo)
    }
    
    private func handleMiss() {
        handleHit(.miss)
    }
    
    // MARK: - MIDI Manager Delegate
    func midiManager(_ manager: MIDIManager, didReceiveNoteOn note: UInt8, velocity: UInt8) {
        guard let keyIndex = MIDIKeyMapping.keyIndex(for: note) else { return }

        // Play key sound
        audioManager.playKeySound(for: keyIndex)

        DispatchQueue.main.async {
            if let judgment = self.checkHit(for: keyIndex) {
                self.handleHit(judgment)
            }
        }
    }
    
    func midiManager(_ manager: MIDIManager, didReceiveNoteOff note: UInt8) {
        // Handle note off if needed for long notes
    }
    
    func midiManager(_ manager: MIDIManager, didConnectDevice deviceName: String) {
        print("Connected MIDI device: \(deviceName)")
    }
    
    func midiManager(_ manager: MIDIManager, didDisconnectDevice deviceName: String) {
        print("Disconnected MIDI device: \(deviceName)")
    }
    
    // MARK: - Demo Chart Creation
    private func createDemoChart() -> Chart {
        var notes: [Note] = []
        let bpm = 120.0
        let beatInterval = 60.0 / bpm
        
        // Create a simple pattern
        let pattern = [0, 2, 4, 7, 9, 12, 14, 16, 19, 21, 24] // C major scale across 25 keys
        
        for beat in 0..<32 {
            let time = Double(beat) * beatInterval
            let keyIndex = pattern[beat % pattern.count]
            
            let note = Note(
                keyIndex: keyIndex,
                startTime: time,
                duration: nil
            )
            notes.append(note)
        }
        
        return Chart(notes: notes, bpm: bpm)
    }
}
