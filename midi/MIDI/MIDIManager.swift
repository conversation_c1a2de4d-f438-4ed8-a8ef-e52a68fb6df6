//
//  MIDIManager.swift
//  midi
//
//  Created by linco<PERSON> on 7/14/25.
//

import Foundation
import Core<PERSON>DI
import Combine

protocol MIDIManagerDelegate: AnyObject {
    func midiManager(_ manager: MIDIManager, didReceiveNoteOn note: UInt8, velocity: UInt8)
    func midiManager(_ manager: MIDIManager, didReceiveNoteOff note: UInt8)
    func midiManager(_ manager: MIDIManager, didConnectDevice deviceName: String)
    func midiManager(_ manager: MIDIManager, didDisconnectDevice deviceName: String)
}

class MIDIManager: ObservableObject {
    weak var delegate: MIDIManagerDelegate?
    
    @Published var isConnected = false
    @Published var connectedDevices: [String] = []
    @Published var pressedKeys: Set<Int> = []
    
    private var midiClient: MIDIClientRef = 0
    private var inputPort: MIDIPortRef = 0
    private var sources: [MIDIEndpointRef] = []
    
    init() {
        // Delay MIDI setup to avoid initialization crashes
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.setupMIDI()
        }
    }
    
    deinit {
        cleanup()
    }
    
    private func setupMIDI() {
        do {
            var status = MIDIClientCreateWithBlock("MIDIGameClient" as CFString, &midiClient) { [weak self] notification in
                self?.handleMIDINotification(notification)
            }

            guard status == noErr else {
                print("Failed to create MIDI client: \(status)")
                return
            }

            status = MIDIInputPortCreateWithBlock(midiClient, "MIDIGameInputPort" as CFString, &inputPort) { [weak self] packetList, srcConnRefCon in
                self?.handleMIDIPacketList(packetList)
            }

            guard status == noErr else {
                print("Failed to create MIDI input port: \(status)")
                return
            }

            connectToAllSources()
            print("MIDI setup completed successfully")
        } catch {
            print("MIDI setup failed: \(error)")
        }
    }
    
    private func connectToAllSources() {
        let sourceCount = MIDIGetNumberOfSources()
        sources.removeAll()
        connectedDevices.removeAll()
        
        for i in 0..<sourceCount {
            let source = MIDIGetSource(i)
            sources.append(source)
            
            let status = MIDIPortConnectSource(inputPort, source, nil)
            if status == noErr {
                if let deviceName = getDeviceName(for: source) {
                    connectedDevices.append(deviceName)
                    delegate?.midiManager(self, didConnectDevice: deviceName)
                }
            }
        }
        
        DispatchQueue.main.async {
            self.isConnected = !self.connectedDevices.isEmpty
        }
    }
    
    private func getDeviceName(for endpoint: MIDIEndpointRef) -> String? {
        var name: Unmanaged<CFString>?
        let status = MIDIObjectGetStringProperty(endpoint, kMIDIPropertyName, &name)
        
        if status == noErr, let cfString = name?.takeRetainedValue() {
            return cfString as String
        }
        return nil
    }
    
    private func handleMIDINotification(_ notification: UnsafePointer<MIDINotification>) {
        switch notification.pointee.messageID {
        case .msgObjectAdded, .msgObjectRemoved:
            DispatchQueue.main.async {
                self.connectToAllSources()
            }
        default:
            break
        }
    }
    
    private func handleMIDIPacketList(_ packetList: UnsafePointer<MIDIPacketList>) {
        let packets = MIDIPacketListGenerator(packetList: packetList)
        
        for packet in packets {
            handleMIDIPacket(packet)
        }
    }
    
    private func handleMIDIPacket(_ packet: MIDIPacket) {
        let data = withUnsafePointer(to: packet.data) {
            $0.withMemoryRebound(to: UInt8.self, capacity: Int(packet.length)) {
                Array(UnsafeBufferPointer(start: $0, count: Int(packet.length)))
            }
        }
        
        guard data.count >= 3 else { return }
        
        let status = data[0]
        let note = data[1]
        let velocity = data[2]
        
        // Check if it's a note on/off message
        let messageType = status & 0xF0
        
        switch messageType {
        case 0x90: // Note On
            if velocity > 0 {
                handleNoteOn(note: note, velocity: velocity)
            } else {
                handleNoteOff(note: note)
            }
        case 0x80: // Note Off
            handleNoteOff(note: note)
        default:
            break
        }
    }
    
    private func handleNoteOn(note: UInt8, velocity: UInt8) {
        guard let keyIndex = MIDIKeyMapping.keyIndex(for: note) else { return }
        
        DispatchQueue.main.async {
            self.pressedKeys.insert(keyIndex)
        }
        
        delegate?.midiManager(self, didReceiveNoteOn: note, velocity: velocity)
    }
    
    private func handleNoteOff(note: UInt8) {
        guard let keyIndex = MIDIKeyMapping.keyIndex(for: note) else { return }
        
        DispatchQueue.main.async {
            self.pressedKeys.remove(keyIndex)
        }
        
        delegate?.midiManager(self, didReceiveNoteOff: note)
    }
    
    private func cleanup() {
        if inputPort != 0 {
            MIDIPortDispose(inputPort)
        }
        if midiClient != 0 {
            MIDIClientDispose(midiClient)
        }
    }
}

// MARK: - MIDI Packet List Generator
struct MIDIPacketListGenerator: Sequence {
    let packetList: UnsafePointer<MIDIPacketList>
    
    func makeIterator() -> MIDIPacketIterator {
        return MIDIPacketIterator(packetList: packetList)
    }
}

struct MIDIPacketIterator: IteratorProtocol {
    private var currentPacket: UnsafePointer<MIDIPacket>?
    private var remainingPackets: UInt32

    init(packetList: UnsafePointer<MIDIPacketList>) {
        remainingPackets = packetList.pointee.numPackets
        currentPacket = remainingPackets > 0 ? withUnsafePointer(to: packetList.pointee.packet) { $0 } : nil
    }

    mutating func next() -> MIDIPacket? {
        guard remainingPackets > 0, let packet = currentPacket else {
            return nil
        }

        let result = packet.pointee
        remainingPackets -= 1

        if remainingPackets > 0 {
            currentPacket = UnsafePointer(MIDIPacketNext(packet))
        } else {
            currentPacket = nil
        }

        return result
    }
}
